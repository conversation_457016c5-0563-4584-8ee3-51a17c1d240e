// CONSTANTS

import { IEnquiryDocument } from '@/server/services/enquiry.service';
import { School, GraduationCap, Music, Globe, Code, Award, FileText } from 'lucide-react';

export const statusThemes = {
  active: {
    primary: 'from-blue-500 to-blue-600',
    light: 'from-blue-50 to-blue-100',
    accent: 'bg-blue-500',
    text: 'text-blue-600',
    bg: 'bg-blue-50',
    border: 'border-blue-100',
    hover: 'hover:bg-blue-100',
    icon: 'text-blue-500',
    label: 'Active',
  },
  converted: {
    primary: 'from-green-500 to-green-600',
    light: 'from-green-50 to-green-100',
    accent: 'bg-green-500',
    text: 'text-green-600',
    bg: 'bg-green-50',
    border: 'border-green-100',
    hover: 'hover:bg-green-100',
    icon: 'text-green-500',
    label: 'Converted',
  },
  closed: {
    primary: 'from-red-500 to-red-600',
    light: 'from-red-50 to-red-100',
    accent: 'bg-red-500',
    text: 'text-red-600',
    bg: 'bg-red-50',
    border: 'border-red-100',
    hover: 'hover:bg-red-100',
    icon: 'text-red-500',
    label: 'Closed',
  },
};

// UTILITY FUNCTIONS

export const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'schools':
      return School;
    case 'colleges':
      return GraduationCap;
    case 'hobbies':
      return Music;
    case 'languages':
      return Globe;
    case 'it_courses':
      return Code;
    case 'competitive_exams':
    case 'entrance_exams':
      return Award;
    default:
      return FileText;
  }
};

export const getStatusInfo = (status: string) => {
  switch (status) {
    case 'pending':
      return {
        color: 'bg-blue-500',
        textColor: 'text-blue-600',
        bgLight: 'bg-blue-50',
        borderColor: 'border-blue-200',
        gradient: 'from-blue-500 to-blue-600',
      };
    case 'completed':
      return {
        color: 'bg-green-500',
        textColor: 'text-green-600',
        bgLight: 'bg-green-50',
        borderColor: 'border-green-200',
        gradient: 'from-green-500 to-green-600',
      };
    default:
      return {
        color: 'bg-red-500',
        textColor: 'text-red-600',
        bgLight: 'bg-red-50',
        borderColor: 'border-red-200',
        gradient: 'from-red-500 to-red-600',
      };
  }
};

export const getSubjectDisplay = (enquiry: IEnquiryDocument): string => {
  if (enquiry.category === 'schools') {
    if (Array.isArray(enquiry.subjectDetails) && enquiry.subjectDetails.length > 0) {
      return enquiry.subjectDetails.map((subject) => subject.name).join(', ');
    } else if (Array.isArray(enquiry.subjects)) {
      return enquiry.subjects.length > 0 ? enquiry.subjects.join(', ') : 'No subjects';
    }
  }

  if (enquiry.category === 'colleges') {
    if (Array.isArray(enquiry.collegeSubjectDetails) && enquiry.collegeSubjectDetails.length > 0) {
      return enquiry.collegeSubjectDetails.map((subject) => subject.name).join(', ');
    } else if (Array.isArray(enquiry.collegeSubjects)) {
      return enquiry.collegeSubjects.length > 0 ? enquiry.collegeSubjects.join(', ') : 'No subjects';
    }
  }

  if (enquiry.category === 'exams' && Array.isArray(enquiry.examSubjects)) {
    return enquiry.examSubjects.length > 0 ? enquiry.examSubjects.join(', ') : 'No subjects';
  }

  if (enquiry.category === 'hobbies' && enquiry.hobby) {
    return enquiry.hobby;
  }

  if (enquiry.category === 'languages' && enquiry.language) {
    return enquiry.language;
  }

  if (enquiry.category === 'it_courses' && enquiry.course) {
    return enquiry.course;
  }

  return 'Not specified';
};
