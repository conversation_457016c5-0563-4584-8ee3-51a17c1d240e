"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(admin)/ascrm/(cms)/(college)/college-subjects/page",{

/***/ "(app-pages-browser)/./lib/react-query/queryKeys.ts":
/*!**************************************!*\
  !*** ./lib/react-query/queryKeys.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QUERY_KEYS: function() { return /* binding */ QUERY_KEYS; }\n/* harmony export */ });\nconst QUERY_KEYS = {\n    AUTH: {\n        SESSION: \"session\"\n    },\n    USER: {\n        PROFILE: \"user-profile\",\n        GENERAL_INFO: \"user-general-info\",\n        ADDRESSES: \"user-addresses\",\n        ADDRESS: \"user-address\"\n    },\n    STAFF: {\n        PROFILE: \"staff-profile\",\n        LIST: \"staff-list\",\n        DETAIL: \"staff-detail\"\n    },\n    PROFILE: {\n        CHILD_PROFILES: \"child-profiles\",\n        CHILD_PROFILE: \"child-profile\",\n        EDUCATION_DETAILS: \"education-details\",\n        EDUCATION_DETAIL: \"education-detail\"\n    },\n    EDUCATION: {\n        // Unified education service\n        SERVICE_CATEGORIES: \"education-service-categories\",\n        SERVICE_CATEGORY: \"education-service-category\",\n        // School\n        BOARDS: \"education-boards\",\n        BOARD: \"education-board\",\n        CLASSES: \"education-classes\",\n        CLASSES_BY_BOARD: \"education-classes-by-board\",\n        CLASS: \"education-class\",\n        SUBJECTS: \"education-subjects\",\n        SUBJECTS_BY_CLASS: \"education-subjects-by-class\",\n        SUBJECT: \"education-subject\",\n        // College\n        STREAMS: \"education-streams\",\n        STREAM: \"education-stream\",\n        DEGREE_LEVELS: \"education-degree-levels\",\n        DEGREE_LEVEL: \"education-degree-level\",\n        DEGREES: \"education-degrees\",\n        DEGREE: \"education-degree\",\n        BRANCHES: \"education-branches\",\n        BRANCHES_BY_DEGREE: \"education-branches-by-degree\",\n        BRANCH: \"education-branch\",\n        COLLEGE_SUBJECTS: \"education-college-subjects\",\n        COLLEGE_SUBJECTS_BY_BRANCH: \"education-college-subjects-by-branch\",\n        COLLEGE_SUBJECT: \"education-college-subject\",\n        // Hobby\n        HOBBY_TYPES: \"education-hobby-types\",\n        HOBBY_TYPE: \"education-hobby-type\",\n        HOBBIES: \"education-hobbies\",\n        HOBBY: \"education-hobby\",\n        // Language\n        LANGUAGE_TYPES: \"education-language-types\",\n        LANGUAGE_TYPE: \"education-language-type\",\n        LANGUAGES: \"education-languages\",\n        LANGUAGE: \"education-language\",\n        // IT Course\n        COURSE_TYPES: \"education-course-types\",\n        COURSE_TYPE: \"education-course-type\",\n        COURSES: \"education-courses\",\n        COURSE: \"education-course\",\n        // Exam\n        EXAM_CATEGORIES: \"education-exam-categories\",\n        EXAM_CATEGORY: \"education-exam-category\",\n        EXAMS: \"education-exams\",\n        EXAM: \"education-exam\",\n        EXAM_SUBJECTS: \"education-exam-subjects\",\n        EXAM_SUBJECT: \"education-exam-subject\"\n    },\n    ENQUIRY: {\n        SEARCH: \"enquiry-search\",\n        CATEGORY_ITEMS: \"enquiry-category-items\",\n        PARENT_ENQUIRIES: \"parent-enquiries\",\n        PARENT_ENQUIRY: \"parent-enquiry\"\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/react-query/queryKeys.ts\n"));

/***/ })

});