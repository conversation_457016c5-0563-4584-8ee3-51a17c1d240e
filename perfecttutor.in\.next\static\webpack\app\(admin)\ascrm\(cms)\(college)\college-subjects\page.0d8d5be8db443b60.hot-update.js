"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(admin)/ascrm/(cms)/(college)/college-subjects/page",{

/***/ "(app-pages-browser)/./lib/react-query/queryKeys.ts":
/*!**************************************!*\
  !*** ./lib/react-query/queryKeys.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QUERY_KEYS: function() { return /* binding */ QUERY_KEYS; }\n/* harmony export */ });\nconst QUERY_KEYS = {\n    AUTH: {\n        SESSION: \"session\"\n    },\n    USER: {\n        PROFILE: \"user-profile\",\n        GENERAL_INFO: \"user-general-info\",\n        ADDRESSES: \"user-addresses\",\n        ADDRESS: \"user-address\"\n    },\n    STAFF: {\n        PROFILE: \"staff-profile\",\n        LIST: \"staff-list\",\n        DETAIL: \"staff-detail\"\n    },\n    PROFILE: {\n        CHILD_PROFILES: \"child-profiles\",\n        CHILD_PROFILE: \"child-profile\",\n        EDUCATION_DETAILS: \"education-details\",\n        EDUCATION_DETAIL: \"education-detail\"\n    },\n    EDUCATION: {\n        SERVICE_CATEGORIES: \"education-service-categories\",\n        SERVICE_CATEGORY: \"education-service-category\",\n        // School\n        BOARDS: \"education-boards\",\n        BOARD: \"education-board\",\n        CLASSES: \"education-classes\",\n        CLASSES_BY_BOARD: \"education-classes-by-board\",\n        CLASS: \"education-class\",\n        SUBJECTS: \"education-subjects\",\n        SUBJECTS_BY_CLASS: \"education-subjects-by-class\",\n        SUBJECT: \"education-subject\",\n        // College\n        STREAMS: \"education-streams\",\n        STREAM: \"education-stream\",\n        DEGREE_LEVELS: \"education-degree-levels\",\n        DEGREE_LEVEL: \"education-degree-level\",\n        DEGREES: \"education-degrees\",\n        DEGREE: \"education-degree\",\n        BRANCHES: \"education-branches\",\n        BRANCHES_BY_DEGREE: \"education-branches-by-degree\",\n        BRANCH: \"education-branch\",\n        COLLEGE_SUBJECTS: \"education-college-subjects\",\n        COLLEGE_SUBJECTS_BY_BRANCH: \"education-college-subjects-by-branch\",\n        COLLEGE_SUBJECT: \"education-college-subject\",\n        // Hobby\n        HOBBY_TYPES: \"education-hobby-types\",\n        HOBBY_TYPE: \"education-hobby-type\",\n        HOBBIES: \"education-hobbies\",\n        HOBBY: \"education-hobby\",\n        // Language\n        LANGUAGE_TYPES: \"education-language-types\",\n        LANGUAGE_TYPE: \"education-language-type\",\n        LANGUAGES: \"education-languages\",\n        LANGUAGE: \"education-language\",\n        // IT Course\n        COURSE_TYPES: \"education-course-types\",\n        COURSE_TYPE: \"education-course-type\",\n        COURSES: \"education-courses\",\n        COURSE: \"education-course\",\n        // Exam\n        EXAM_CATEGORIES: \"education-exam-categories\",\n        EXAM_CATEGORY: \"education-exam-category\",\n        EXAMS: \"education-exams\",\n        EXAM: \"education-exam\",\n        EXAM_SUBJECTS: \"education-exam-subjects\",\n        EXAM_SUBJECT: \"education-exam-subject\"\n    },\n    ENQUIRY: {\n        SEARCH: \"enquiry-search\",\n        CATEGORY_ITEMS: \"enquiry-category-items\",\n        PARENT_ENQUIRIES: \"parent-enquiries\",\n        PARENT_ENQUIRY: \"parent-enquiry\"\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/react-query/queryKeys.ts\n"));

/***/ })

});