"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(users)/parent-dash/leads/page",{

/***/ "(app-pages-browser)/./hooks/enquiry.hooks.ts":
/*!********************************!*\
  !*** ./hooks/enquiry.hooks.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreateEnquiry: function() { return /* binding */ useCreateEnquiry; },\n/* harmony export */   useGetCategoryItems: function() { return /* binding */ useGetCategoryItems; },\n/* harmony export */   useGetParentEnquiries: function() { return /* binding */ useGetParentEnquiries; },\n/* harmony export */   useGetParentEnquiryById: function() { return /* binding */ useGetParentEnquiryById; },\n/* harmony export */   useSearchEnquiries: function() { return /* binding */ useSearchEnquiries; },\n/* harmony export */   useUpdateEnquiryStatus: function() { return /* binding */ useUpdateEnquiryStatus; }\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _server_services_enquiry_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/server/services/enquiry.service */ \"(app-pages-browser)/./server/services/enquiry.service.ts\");\n/* harmony import */ var _lib_react_query_queryKeys__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/react-query/queryKeys */ \"(app-pages-browser)/./lib/react-query/queryKeys.ts\");\n/* harmony import */ var _lib_react_query_queryClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/react-query/queryClient */ \"(app-pages-browser)/./lib/react-query/queryClient.ts\");\n\n\n\n\nfunction useSearchEnquiries(query) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)({\n        queryKey: [\n            _lib_react_query_queryKeys__WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.ENQUIRY.SEARCH,\n            query\n        ],\n        queryFn: ()=>_server_services_enquiry_service__WEBPACK_IMPORTED_MODULE_0__.enquiryService.search(query),\n        enabled: !!query && query.length > 1\n    });\n}\nfunction useGetCategoryItems(type, id) {\n    let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)({\n        queryKey: [\n            _lib_react_query_queryKeys__WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.ENQUIRY.CATEGORY_ITEMS,\n            type,\n            id\n        ],\n        queryFn: ()=>_server_services_enquiry_service__WEBPACK_IMPORTED_MODULE_0__.enquiryService.getCategoryItems(type, id),\n        enabled: !!type && !!id,\n        ...options\n    });\n}\nfunction useCreateEnquiry() {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationFn: (data)=>_server_services_enquiry_service__WEBPACK_IMPORTED_MODULE_0__.enquiryService.createEnquiry(data),\n        onSuccess: ()=>{\n            _lib_react_query_queryClient__WEBPACK_IMPORTED_MODULE_2__.queryClient.invalidateQueries({\n                queryKey: [\n                    _lib_react_query_queryKeys__WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.ENQUIRY.PARENT_ENQUIRIES\n                ]\n            });\n        }\n    });\n}\nfunction useGetParentEnquiries(params) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)({\n        queryKey: [\n            _lib_react_query_queryKeys__WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.ENQUIRY.PARENT_ENQUIRIES,\n            params\n        ],\n        queryFn: ()=>_server_services_enquiry_service__WEBPACK_IMPORTED_MODULE_0__.enquiryService.getParentEnquiries(params),\n        ...options\n    });\n}\nfunction useGetParentEnquiryById(id) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)({\n        queryKey: [\n            _lib_react_query_queryKeys__WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.ENQUIRY.PARENT_ENQUIRY,\n            id\n        ],\n        queryFn: ()=>_server_services_enquiry_service__WEBPACK_IMPORTED_MODULE_0__.enquiryService.getParentEnquiryById(id),\n        enabled: !!id,\n        ...options\n    });\n}\nfunction useUpdateEnquiryStatus() {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, isActive } = param;\n            return _server_services_enquiry_service__WEBPACK_IMPORTED_MODULE_0__.enquiryService.updateEnquiryStatus(id, isActive);\n        },\n        onSuccess: (_, variables)=>{\n            _lib_react_query_queryClient__WEBPACK_IMPORTED_MODULE_2__.queryClient.invalidateQueries({\n                queryKey: [\n                    _lib_react_query_queryKeys__WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.ENQUIRY.PARENT_ENQUIRIES\n                ]\n            });\n            _lib_react_query_queryClient__WEBPACK_IMPORTED_MODULE_2__.queryClient.invalidateQueries({\n                queryKey: [\n                    _lib_react_query_queryKeys__WEBPACK_IMPORTED_MODULE_1__.QUERY_KEYS.ENQUIRY.PARENT_ENQUIRY,\n                    variables.id\n                ]\n            });\n        }\n    });\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/enquiry.hooks.ts\n"));

/***/ })

});