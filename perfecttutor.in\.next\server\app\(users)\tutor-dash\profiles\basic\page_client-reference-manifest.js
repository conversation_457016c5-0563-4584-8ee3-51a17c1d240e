globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/(users)/tutor-dash/profiles/basic/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./app/(admin)/ascrm/(cms)/(college)/college-subjects/college-subject-table.tsx":{"*":{"id":"(ssr)/./app/(admin)/ascrm/(cms)/(college)/college-subjects/college-subject-table.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/dashboard/ascrm/misc/StateUI.tsx":{"*":{"id":"(ssr)/./components/dashboard/ascrm/misc/StateUI.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(admin)/ascrm/LayoutWrapper.tsx":{"*":{"id":"(ssr)/./app/(admin)/ascrm/LayoutWrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/dashboard/ascrm/misc/AdminDashActionLinks.tsx":{"*":{"id":"(ssr)/./components/dashboard/ascrm/misc/AdminDashActionLinks.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/dashboard/ascrm/misc/HeadingBar.tsx":{"*":{"id":"(ssr)/./components/dashboard/ascrm/misc/HeadingBar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/dashboard/ascrm/misc/MasterFilter.tsx":{"*":{"id":"(ssr)/./components/dashboard/ascrm/misc/MasterFilter.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/dashboard/ascrm/misc/NavBar.tsx":{"*":{"id":"(ssr)/./components/dashboard/ascrm/misc/NavBar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/dashboard/ascrm/misc/Pagination.tsx":{"*":{"id":"(ssr)/./components/dashboard/ascrm/misc/Pagination.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/dashboard/ascrm/misc/SideBar.tsx":{"*":{"id":"(ssr)/./components/dashboard/ascrm/misc/SideBar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/select.tsx":{"*":{"id":"(ssr)/./components/ui/select.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./lib/react-query/QueryProvider.tsx":{"*":{"id":"(ssr)/./lib/react-query/QueryProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-toastify/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/react-toastify/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(users)/parent-dash/leads/_client.tsx":{"*":{"id":"(ssr)/./app/(users)/parent-dash/leads/_client.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/dashboard/parent-dash/misc/NavBar.tsx":{"*":{"id":"(ssr)/./components/dashboard/parent-dash/misc/NavBar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/dashboard/parent-dash/misc/SideBar.tsx":{"*":{"id":"(ssr)/./components/dashboard/parent-dash/misc/SideBar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(auth)/login/_client.tsx":{"*":{"id":"(ssr)/./app/(auth)/login/_client.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(users)/parent-dash/profiles/education/_client.tsx":{"*":{"id":"(ssr)/./app/(users)/parent-dash/profiles/education/_client.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(users)/parent-dash/profiles/basic/_client.tsx":{"*":{"id":"(ssr)/./app/(users)/parent-dash/profiles/basic/_client.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(users)/parent-dash/support/complaints/page.tsx":{"*":{"id":"(ssr)/./app/(users)/parent-dash/support/complaints/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(users)/tutor-dash/_helper.tsx":{"*":{"id":"(ssr)/./app/(users)/tutor-dash/_helper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/dashboard/tutor-dash/misc/NavBar.tsx":{"*":{"id":"(ssr)/./components/dashboard/tutor-dash/misc/NavBar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/dashboard/tutor-dash/misc/SideBar.tsx":{"*":{"id":"(ssr)/./components/dashboard/tutor-dash/misc/SideBar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(users)/tutor-dash/profiles/basic/page.tsx":{"*":{"id":"(ssr)/./app/(users)/tutor-dash/profiles/basic/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(admin)/ascrm/(cms)/service-categories/service-table.tsx":{"*":{"id":"(ssr)/./app/(admin)/ascrm/(cms)/service-categories/service-table.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(admin)/ascrm/(cms)/(course)/course-types/course-type-table.tsx":{"*":{"id":"(ssr)/./app/(admin)/ascrm/(cms)/(course)/course-types/course-type-table.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(admin)/ascrm/(cms)/(course)/courses/course-table.tsx":{"*":{"id":"(ssr)/./app/(admin)/ascrm/(cms)/(course)/courses/course-table.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\app\\(admin)\\ascrm\\(cms)\\(college)\\college-subjects\\college-subject-table.tsx":{"id":"(app-pages-browser)/./app/(admin)/ascrm/(cms)/(college)/college-subjects/college-subject-table.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\components\\dashboard\\ascrm\\misc\\StateUI.tsx":{"id":"(app-pages-browser)/./components/dashboard/ascrm/misc/StateUI.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\app\\(admin)\\ascrm\\LayoutWrapper.tsx":{"id":"(app-pages-browser)/./app/(admin)/ascrm/LayoutWrapper.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/(users)/tutor-dash/layout","static/chunks/app/(users)/tutor-dash/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\components\\dashboard\\ascrm\\misc\\AdminDashActionLinks.tsx":{"id":"(app-pages-browser)/./components/dashboard/ascrm/misc/AdminDashActionLinks.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\components\\dashboard\\ascrm\\misc\\HeadingBar.tsx":{"id":"(app-pages-browser)/./components/dashboard/ascrm/misc/HeadingBar.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\components\\dashboard\\ascrm\\misc\\MasterFilter.tsx":{"id":"(app-pages-browser)/./components/dashboard/ascrm/misc/MasterFilter.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\components\\dashboard\\ascrm\\misc\\NavBar.tsx":{"id":"(app-pages-browser)/./components/dashboard/ascrm/misc/NavBar.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\components\\dashboard\\ascrm\\misc\\Pagination.tsx":{"id":"(app-pages-browser)/./components/dashboard/ascrm/misc/Pagination.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\components\\dashboard\\ascrm\\misc\\SideBar.tsx":{"id":"(app-pages-browser)/./components/dashboard/ascrm/misc/SideBar.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\components\\ui\\select.tsx":{"id":"(app-pages-browser)/./components/ui/select.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\lib\\react-query\\QueryProvider.tsx":{"id":"(app-pages-browser)/./lib/react-query/QueryProvider.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\node_modules\\next\\dist\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/(users)/tutor-dash/layout","static/chunks/app/(users)/tutor-dash/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/(users)/tutor-dash/layout","static/chunks/app/(users)/tutor-dash/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\(admin)\\\\ascrm\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\(admin)\\\\ascrm\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\node_modules\\react-toastify\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/react-toastify/dist/index.mjs","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\node_modules\\react-toastify\\dist\\ReactToastify.css":{"id":"(app-pages-browser)/./node_modules/react-toastify/dist/ReactToastify.css","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\app\\(users)\\parent-dash\\leads\\_client.tsx":{"id":"(app-pages-browser)/./app/(users)/parent-dash/leads/_client.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\components\\dashboard\\parent-dash\\misc\\NavBar.tsx":{"id":"(app-pages-browser)/./components/dashboard/parent-dash/misc/NavBar.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\components\\dashboard\\parent-dash\\misc\\SideBar.tsx":{"id":"(app-pages-browser)/./components/dashboard/parent-dash/misc/SideBar.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\(users)\\\\parent-dash\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\(users)\\\\parent-dash\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\app\\(auth)\\login\\_client.tsx":{"id":"(app-pages-browser)/./app/(auth)/login/_client.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\(auth)\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\(auth)\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/(auth)/layout","static/chunks/app/(auth)/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\app\\(users)\\parent-dash\\profiles\\education\\_client.tsx":{"id":"(app-pages-browser)/./app/(users)/parent-dash/profiles/education/_client.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\app\\(users)\\parent-dash\\profiles\\basic\\_client.tsx":{"id":"(app-pages-browser)/./app/(users)/parent-dash/profiles/basic/_client.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\app\\(users)\\parent-dash\\support\\complaints\\page.tsx":{"id":"(app-pages-browser)/./app/(users)/parent-dash/support/complaints/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\app\\(users)\\tutor-dash\\_helper.tsx":{"id":"(app-pages-browser)/./app/(users)/tutor-dash/_helper.tsx","name":"*","chunks":["app/(users)/tutor-dash/page","static/chunks/app/(users)/tutor-dash/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\components\\dashboard\\tutor-dash\\misc\\NavBar.tsx":{"id":"(app-pages-browser)/./components/dashboard/tutor-dash/misc/NavBar.tsx","name":"*","chunks":["app/(users)/tutor-dash/layout","static/chunks/app/(users)/tutor-dash/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\components\\dashboard\\tutor-dash\\misc\\SideBar.tsx":{"id":"(app-pages-browser)/./components/dashboard/tutor-dash/misc/SideBar.tsx","name":"*","chunks":["app/(users)/tutor-dash/layout","static/chunks/app/(users)/tutor-dash/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\(users)\\\\tutor-dash\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\(users)\\\\tutor-dash\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/(users)/tutor-dash/layout","static/chunks/app/(users)/tutor-dash/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\app\\(users)\\tutor-dash\\profiles\\basic\\page.tsx":{"id":"(app-pages-browser)/./app/(users)/tutor-dash/profiles/basic/page.tsx","name":"*","chunks":["app/(users)/tutor-dash/profiles/basic/page","static/chunks/app/(users)/tutor-dash/profiles/basic/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\app\\(admin)\\ascrm\\(cms)\\service-categories\\service-table.tsx":{"id":"(app-pages-browser)/./app/(admin)/ascrm/(cms)/service-categories/service-table.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\app\\(admin)\\ascrm\\(cms)\\(course)\\course-types\\course-type-table.tsx":{"id":"(app-pages-browser)/./app/(admin)/ascrm/(cms)/(course)/course-types/course-type-table.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\app\\(admin)\\ascrm\\(cms)\\(course)\\courses\\course-table.tsx":{"id":"(app-pages-browser)/./app/(admin)/ascrm/(cms)/(course)/courses/course-table.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\":[],"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\app\\(auth)\\layout":["static/css/app/(auth)/layout.css"],"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\app\\(users)\\tutor-dash\\page":["static/css/app/(users)/tutor-dash/page.css"],"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\app\\(users)\\tutor-dash\\layout":["static/css/app/(users)/tutor-dash/layout.css"],"C:\\Users\\<USER>\\Desktop\\Projects\\office\\app-pt\\perfecttutor.in\\app\\(users)\\tutor-dash\\profiles\\basic\\page":["static/css/app/(users)/tutor-dash/profiles/basic/page.css"]}}