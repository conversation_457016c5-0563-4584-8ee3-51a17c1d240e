// Base interface for education items
export interface IEducationItem {
  _id: string;
  name: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Metadata interfaces for different service types
export interface ISchoolMetadata {
  class: {
    _id: string;
    name: string;
    displayOrder: number;
    board: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };
  board: {
    _id: string;
    name: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };
}

export interface ICollegeMetadata {
  branch: {
    _id: string;
    name: string;
    degree: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };
  degree: {
    _id: string;
    name: string;
    degreeLevel: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };
  degreeLevel: {
    _id: string;
    name: string;
    stream: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };
  stream: {
    _id: string;
    name: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };
}

export interface ILanguageMetadata {
  languageType: {
    _id: string;
    name: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };
}

export interface IHobbyMetadata {
  hobbyType: {
    _id: string;
    name: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };
}

export interface ICourseMetadata {
  courseType: {
    _id: string;
    name: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };
}

export interface IExamMetadata {
  exam: {
    _id: string;
    name: string;
    examCategory: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };
  examCategory: {
    _id: string;
    name: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };
}

// Union type for all possible metadata
export type ICategoryMetadata = 
  | ISchoolMetadata 
  | ICollegeMetadata 
  | ILanguageMetadata 
  | IHobbyMetadata 
  | ICourseMetadata 
  | IExamMetadata;

// Main response interface for getCategoryItems
export interface ICategoryItemsResponse {
  // Items arrays (only one will be populated based on type)
  subjects?: IEducationItem[];
  languages?: IEducationItem[];
  hobbies?: IEducationItem[];
  courses?: IEducationItem[];
  examSubjects?: IEducationItem[];
  
  // Metadata (contains hierarchical information)
  metadata: ICategoryMetadata | null;
}

// Type guards to check metadata type
export function isSchoolMetadata(metadata: ICategoryMetadata): metadata is ISchoolMetadata {
  return 'class' in metadata && 'board' in metadata;
}

export function isCollegeMetadata(metadata: ICategoryMetadata): metadata is ICollegeMetadata {
  return 'branch' in metadata && 'degree' in metadata && 'degreeLevel' in metadata && 'stream' in metadata;
}

export function isLanguageMetadata(metadata: ICategoryMetadata): metadata is ILanguageMetadata {
  return 'languageType' in metadata;
}

export function isHobbyMetadata(metadata: ICategoryMetadata): metadata is IHobbyMetadata {
  return 'hobbyType' in metadata;
}

export function isCourseMetadata(metadata: ICategoryMetadata): metadata is ICourseMetadata {
  return 'courseType' in metadata;
}

export function isExamMetadata(metadata: ICategoryMetadata): metadata is IExamMetadata {
  return 'exam' in metadata && 'examCategory' in metadata;
}

// Helper type for form options
export interface IEducationItemOption {
  id: string;
  value: string;
  label: string;
}

// Helper function to convert education items to options
export function createEducationItemOptions(items: IEducationItem[]): IEducationItemOption[] {
  return items.map((item) => ({
    id: item._id,
    value: item._id,
    label: item.name,
  }));
}
