'use client';

import { useState, useEffect } from 'react';
import { Search, MapPin, BookA, School, GraduationCap, ArrowLeft, Users, Clock, MessageSquare } from 'lucide-react';
import { Al<PERSON>, FormHeading, PrimaryModalWithHeader, TinyLoader } from '@/components/dashboard/shared/misc';
import { PrimaryInput, PrimarySelect, PrimaryTextarea, SubmitButton, PrimaryMultiSelectForm } from '@/components/forms';
import { useSearchEnquiries, useCreateEnquiry } from '@/hooks/enquiry.hooks';
import { useGetAllChildProfiles } from '@/hooks/profile/profile.hooks';
import { useGetCategoryItems } from '@/hooks/enquiry.hooks';
import { createEducationItemOptions } from '@/server/services/enquiry.service';
import { Form } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'react-toastify';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { deliveryModeOptions, startTimeOptions, tutorGenderOptions } from '@/validation/schemas/enquiry.maps';
import { ISearchResponse } from '@/server/services/enquiry.service';
import { createEnquirySchema, CreateEnquiryInput } from '@/validation/schemas/enquiry.schema';
import { serviceCategoryMap } from '@/validation/schemas/education/index.maps';

interface AddEnquiryModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const AddEnquiryModal = ({ isOpen, onClose }: AddEnquiryModalProps) => {
  const [activeTab, setActiveTab] = useState('search');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMatch, setSelectedMatch] = useState<ISearchResponse['matches'][0] | null>(null);

  const TABS = {
    SEARCH: 'search',
    LOCATION_SUBJECTS: 'location-subjects',
    TUTOR_TIMING: 'tutor-timing',
    MESSAGE_STUDENT: 'message-student',
  };

  const { data: searchResults, isLoading: isSearching } = useSearchEnquiries(searchTerm.length > 1 ? searchTerm : '');
  const { data: childProfilesData } = useGetAllChildProfiles();
  const createEnquiry = useCreateEnquiry();

  const childProfiles = childProfilesData?.data?.childProfiles || [];
  const childProfileOptions = childProfiles.map((profile) => ({ value: profile._id, label: profile.fullName }));

  const getParentId = () => {
    if (!selectedMatch) return '';

    switch (selectedMatch.type) {
      case 'schools':
        return selectedMatch.details.class?.id || '';
      case 'colleges':
        return selectedMatch.details.branch?.id || '';
      case 'exams':
        return selectedMatch.details.exam?.id || '';
      case 'languages':
        return selectedMatch.details.languageType?.id || '';
      case 'hobbies':
        return selectedMatch.details.hobbyType?.id || '';
      case 'it_courses':
        return selectedMatch.details.courseCategory?.id || selectedMatch.details.course?.id || '';
      default:
        return '';
    }
  };

  const parentId = getParentId();
  const { data: categoryItemsData } = useGetCategoryItems(selectedMatch?.type || 'schools', parentId, { enabled: !!selectedMatch && !!parentId });

  const getEducationItems = () => {
    if (!categoryItemsData?.success || !categoryItemsData.data) return [];

    const data = categoryItemsData.data;
    switch (selectedMatch?.type) {
      case 'schools':
        return data.subjects || [];
      case 'colleges':
        return data.subjects || [];
      case 'languages':
        return data.languages || [];
      case 'hobbies':
        return data.hobbies || [];
      case 'it_courses':
        return data.courses || [];
      case 'exams':
        return data.examSubjects || [];
      default:
        return [];
    }
  };

  const educationItems = getEducationItems();
  const educationItemOptions = createEducationItemOptions(educationItems);

  useEffect(() => {
    if (selectedMatch && parentId) {
      if (selectedMatch.type === 'colleges') {
        const degreeLevel = categoryItemsData?.data?.metadata?.degreeLevel?._id;
        const stream = categoryItemsData?.data?.metadata?.stream?._id;

        if (stream && degreeLevel) {
          form.setValue('stream', stream);
          form.setValue('degreeLevel', degreeLevel);
        }
      } else {
        form.setValue('stream', '');
        form.setValue('degreeLevel', '');
      }
    }
  }, [selectedMatch, parentId, categoryItemsData, educationItems, educationItemOptions]);

  const form = useForm<CreateEnquiryInput>({
    resolver: zodResolver(createEnquirySchema),
    defaultValues: {
      childProfileId: childProfiles[0]?._id || '',
      preferences: {
        location: { address: '', landmark: '' },
        tutorGender: 'any',
        classesPerWeek: 2,
        startTime: 'immediately',
        deliveryModes: ['student_house'],
        specialRequirements: '',
      },
      category: selectedMatch?.type ? (serviceCategoryMap[selectedMatch.type]?.key as any) : 'schools',
    },
  });

  const handleSelectMatch = (match: ISearchResponse['matches'][0]) => {
    setSelectedMatch(match);
    setActiveTab(TABS.LOCATION_SUBJECTS);

    const matchDetails = match.details || {};

    if (match.type === 'schools') {
      form.reset({
        childProfileId: childProfiles[0]?._id || '',
        preferences: {
          location: { address: '', landmark: '' },
          tutorGender: 'any' as const,
          classesPerWeek: 2,
          startTime: 'immediately',
          deliveryModes: ['student_house'],
          specialRequirements: '',
        },
        category: 'schools' as const,
        board: matchDetails.board?.id,
        class: matchDetails.class?.id,
        subjects: [],
      });
    } else if (match.type === 'colleges') {
      form.reset({
        childProfileId: childProfiles[0]?._id || '',
        preferences: {
          location: { address: '', landmark: '' },
          tutorGender: 'any' as const,
          classesPerWeek: 2,
          startTime: 'immediately',
          deliveryModes: ['student_house'],
          specialRequirements: '',
        },
        category: 'colleges' as const,
        degree: matchDetails.degree?.id,
        branch: matchDetails.branch?.id,
        collegeSubjects: [],
      });
    } else if (match.type === 'hobbies') {
      form.reset({
        childProfileId: childProfiles[0]?._id || '',
        preferences: {
          location: { address: '', landmark: '' },
          tutorGender: 'any' as const,
          classesPerWeek: 2,
          startTime: 'immediately',
          deliveryModes: ['student_house'],
          specialRequirements: '',
        },
        category: 'hobbies' as const,
        hobbyType: matchDetails.hobbyType?.id,
        hobby: matchDetails.hobby?.id,
      });
    } else if (match.type === 'languages') {
      form.reset({
        childProfileId: childProfiles[0]?._id || '',
        preferences: {
          location: { address: '', landmark: '' },
          tutorGender: 'any' as const,
          classesPerWeek: 2,
          startTime: 'immediately',
          deliveryModes: ['student_house'],
          specialRequirements: '',
        },
        category: 'languages' as const,
        languageType: matchDetails.languageType?.id,
        language: matchDetails.language?.id,
      });
    } else if (match.type === 'it_courses') {
      form.reset({
        childProfileId: childProfiles[0]?._id || '',
        preferences: {
          location: { address: '', landmark: '' },
          tutorGender: 'any' as const,
          classesPerWeek: 2,
          startTime: 'immediately',
          deliveryModes: ['student_house'],
          specialRequirements: '',
        },
        category: 'it_courses' as const,
        course: matchDetails.course?.id,
      });
    } else if (match.type === 'exams') {
      form.reset({
        childProfileId: childProfiles[0]?._id || '',
        preferences: {
          location: { address: '', landmark: '' },
          tutorGender: 'any' as const,
          classesPerWeek: 2,
          startTime: 'immediately',
          deliveryModes: ['student_house'],
          specialRequirements: '',
        },
        category: 'exams',
        examCategory: matchDetails.examCategory?.id,
        exam: matchDetails.exam?.id,
        examSubjects: [],
      });
    }
  };

  const onSubmit = async (data: CreateEnquiryInput) => {
    try {
      const response = await createEnquiry.mutateAsync(data);

      if (!response.success) throw new Error(response.message || 'Operation failed');
      toast.success('Enquiry created successfully');
      setActiveTab(TABS.SEARCH);
      setSearchTerm('');
      setSelectedMatch(null);
      onClose();
    } catch (error: any) {
      toast.error(error.message || 'Failed to create enquiry');
      console.error(error);
    }
  };

  const getModalProps = () => {
    switch (activeTab) {
      case TABS.SEARCH:
        return {
          title: 'Find your Tutor or Institute',
          subtitle: 'Get Qualified Tutors & Institutes Online or Near You',
          icon: <Search className='text-white' size={20} />,
        };
      case TABS.LOCATION_SUBJECTS:
        return {
          title: 'Fill Your Location & Subjects',
          subtitle: 'Provide these details to find perfect tutor',
          icon: <MapPin className='text-white' size={20} />,
        };
      case TABS.TUTOR_TIMING:
        return {
          title: 'Fill Your Requirements',
          subtitle: 'Set your preferences for tutor and institute',
          icon: <Users className='text-white' size={20} />,
        };
      case TABS.MESSAGE_STUDENT:
        return {
          title: 'Additional Details',
          subtitle: 'Add special requirements and select student',
          icon: <MessageSquare className='text-white' size={20} />,
        };
      default:
        return {
          title: 'Create Tuition Enquiry',
          subtitle: 'Fill in the details for your tuition enquiry',
          icon: <BookA className='text-white' size={20} />,
        };
    }
  };

  useEffect(() => {
    if (!selectedMatch && activeTab !== TABS.SEARCH) {
      form.reset({
        childProfileId: childProfiles[0]?._id || '',
        preferences: {
          location: { address: '', landmark: '' },
          tutorGender: 'any',
          classesPerWeek: 2,
          startTime: 'immediately',
          deliveryModes: ['student_house'],
          specialRequirements: '',
        },
        category: 'schools',
      });
    }
  }, [activeTab, selectedMatch, form, childProfiles, TABS.SEARCH]);

  const validateLocationSubjectsTab = () => {
    const address = form.getValues('preferences.location.address');

    if (selectedMatch?.type === 'schools') {
      const subjects = form.getValues('subjects') || [];
      return address && subjects.length > 0;
    } else if (selectedMatch?.type === 'colleges') {
      const collegeSubjects = form.getValues('collegeSubjects') || [];
      return address && collegeSubjects.length > 0;
    } else if (selectedMatch?.type === 'exams') {
      const examSubjects = form.getValues('examSubjects') || [];
      return address && examSubjects.length > 0;
    } else if (selectedMatch?.type === 'languages') {
      const language = form.getValues('language');
      return address && !!language;
    } else if (selectedMatch?.type === 'hobbies') {
      const hobby = form.getValues('hobby');
      return address && !!hobby;
    } else if (selectedMatch?.type === 'it_courses') {
      const course = form.getValues('course');
      return address && !!course;
    }

    return !!address;
  };

  useEffect(() => {
    if (form.getValues('preferences.deliveryModes').includes('institute')) {
      form.setValue('preferences.tutorGender', 'any');
    }
  }, [form.getValues('preferences.deliveryModes')]);

  const validateTutorTimingTab = () => {
    const tutorGender = form.getValues('preferences.tutorGender');
    const classesPerWeek = form.getValues('preferences.classesPerWeek');
    const startTime = form.getValues('preferences.startTime');
    const deliveryModes = form.getValues('preferences.deliveryModes') || [];

    return !!tutorGender && !!classesPerWeek && !!startTime && deliveryModes.length > 0;
  };

  const validateMessageStudentTab = () => {
    const childProfileId = form.getValues('childProfileId');
    return !!childProfileId;
  };

  const isCurrentTabValid = () => {
    switch (activeTab) {
      case TABS.LOCATION_SUBJECTS:
        return validateLocationSubjectsTab();
      case TABS.TUTOR_TIMING:
        return validateTutorTimingTab();
      case TABS.MESSAGE_STUDENT:
        return validateMessageStudentTab();
      default:
        return true;
    }
  };

  const goToNextTab = () => {
    form.trigger();

    if (!isCurrentTabValid()) {
      toast.error('Please fill in all required fields');
      return;
    }

    switch (activeTab) {
      case TABS.LOCATION_SUBJECTS:
        setActiveTab(TABS.TUTOR_TIMING);
        break;
      case TABS.TUTOR_TIMING:
        setActiveTab(TABS.MESSAGE_STUDENT);
        break;
      case TABS.MESSAGE_STUDENT:
        form.handleSubmit(onSubmit)();
        break;
      default:
        break;
    }
  };

  const goToPrevTab = () => {
    switch (activeTab) {
      case TABS.LOCATION_SUBJECTS:
        setActiveTab(TABS.SEARCH);
        break;
      case TABS.TUTOR_TIMING:
        setActiveTab(TABS.LOCATION_SUBJECTS);
        break;
      case TABS.MESSAGE_STUDENT:
        setActiveTab(TABS.TUTOR_TIMING);
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (!selectedMatch) return;

    if (['schools', 'colleges', 'exams'].includes(selectedMatch.type)) {
      const fieldName = selectedMatch.type === 'schools' ? 'subjects' : selectedMatch.type === 'colleges' ? 'collegeSubjects' : 'examSubjects';

      const selected = form.watch(fieldName) || [];

      if (selected.length !== educationItemOptions.length && form.getValues('allSubjects')) {
        form.setValue('allSubjects', false);
      }

      if (selected.length === educationItemOptions.length && !form.getValues('allSubjects')) {
        form.setValue('allSubjects', true);
      }
    }
  }, [form.watch('subjects'), form.watch('collegeSubjects'), form.watch('examSubjects'), selectedMatch, educationItemOptions.length]);

  return (
    <PrimaryModalWithHeader
      isOpen={isOpen}
      onClose={onClose}
      {...getModalProps()}
      variant='primary'
      maxWidth={activeTab === TABS.SEARCH ? 'max-w-2xl' : 'max-w-4xl'}
    >
      <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
        <TabsList className='hidden'>
          <TabsTrigger value={TABS.SEARCH}>Search</TabsTrigger>
          <TabsTrigger value={TABS.LOCATION_SUBJECTS}>Location & Subjects</TabsTrigger>
          <TabsTrigger value={TABS.TUTOR_TIMING}>Tutor & Timing</TabsTrigger>
          <TabsTrigger value={TABS.MESSAGE_STUDENT}>Message & Student</TabsTrigger>
        </TabsList>
        {/* Search Tab */}
        <TabsContent value={TABS.SEARCH} className='p-0 m-0'>
          <div className='p-6 bg-white rounded-b-xl'>
            <div className='mb-6'>
              <div className='relative'>
                <input
                  type='text'
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder='Type your Class, Degree, Hobby, Language, IT Course or Exam...'
                  className='w-full px-4 py-3 pl-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primaryColor-500 focus:border-transparent'
                />
                <Search className='absolute left-3 top-3.5 text-gray-400' size={18} />
              </div>
              {searchTerm.length > 0 && searchTerm.length < 2 && (
                <Alert className='mt-3' type='info' message='Please enter at least 2 characters to search' />
              )}
            </div>

            {/* Search Results */}
            <div>
              <h3 className='text-sm font-medium text-gray-500 mb-3'>Search Results</h3>
              <div className='border border-gray-200 rounded-lg overflow-hidden'>
                {isSearching ? (
                  <TinyLoader message='Searching...' className='min-h-[200px]' />
                ) : searchTerm.length >= 2 && searchResults?.data?.matches && searchResults.data.matches.length < 1 ? (
                  <div className='p-4 text-center text-gray-500'>No results found</div>
                ) : searchTerm.length < 2 ? (
                  <div className='p-4 text-center text-gray-500'>Enter at least 2 characters to search</div>
                ) : (
                  <div className='max-h-60 overflow-y-auto'>
                    {searchResults?.data?.matches &&
                      searchResults.data.matches.map((result, index: number) => (
                        <div
                          key={index}
                          className='p-3 border-b border-gray-200 last:border-b-0 hover:bg-gray-50 cursor-pointer transition-colors'
                          onClick={() => handleSelectMatch(result)}
                        >
                          <div className='font-medium text-sm text-gray-800'>{result.displayText}</div>
                        </div>
                      ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </TabsContent>{' '}
        {/* Location & Subjects Tab */}
        <TabsContent value={TABS.LOCATION_SUBJECTS} className='p-0 m-0'>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className='p-6 bg-white rounded-b-xl'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-10'>
                  {/* Left Column */}
                  <div>
                    {/* Location Details */}
                    <div className='mb-6'>
                      <FormHeading icon={MapPin} title='Location Details' variant='primary' />
                      <div className='space-y-5'>
                        <PrimaryInput
                          form={form}
                          name='preferences.location.address'
                          label='Your Location'
                          placeholder='Enter your full address'
                          required
                        />
                        <PrimaryInput
                          form={form}
                          name='preferences.location.landmark'
                          label='Your Landmark'
                          placeholder='Any nearby landmark (optional)'
                        />
                      </div>
                    </div>
                  </div>

                  {/* Right Column */}
                  <div>
                    {/* Subject Selection */}
                    {selectedMatch && (
                      <div className='mb-6'>
                        <FormHeading
                          icon={selectedMatch.type === 'schools' ? School : selectedMatch.type === 'colleges' ? GraduationCap : BookA}
                          title={
                            selectedMatch.type === 'schools'
                              ? 'School Subjects'
                              : selectedMatch.type === 'colleges'
                              ? 'College Subjects'
                              : selectedMatch.type === 'exams'
                              ? 'Exam Subjects'
                              : selectedMatch.type === 'languages'
                              ? 'Language Selection'
                              : selectedMatch.type === 'hobbies'
                              ? 'Hobby Selection'
                              : 'Course Selection'
                          }
                          variant={
                            selectedMatch.type === 'schools'
                              ? 'blue'
                              : selectedMatch.type === 'colleges'
                              ? 'purple'
                              : selectedMatch.type === 'languages'
                              ? 'secondary'
                              : selectedMatch.type === 'hobbies'
                              ? 'primary'
                              : 'green'
                          }
                        />
                        <Alert
                          type='info'
                          title={
                            selectedMatch.type === 'schools'
                              ? `${selectedMatch.details?.board?.name} - ${selectedMatch.details?.class?.name}`
                              : selectedMatch.type === 'colleges'
                              ? `${selectedMatch.details?.degree?.name} - ${selectedMatch.details?.branch?.name}`
                              : selectedMatch.type === 'exams'
                              ? `${selectedMatch.details?.examCategory?.name} - ${selectedMatch.details?.exam?.name}`
                              : selectedMatch.type === 'languages'
                              ? `${selectedMatch.details?.languageType?.name}`
                              : selectedMatch.type === 'hobbies'
                              ? `${selectedMatch.details?.hobbyType?.name}`
                              : selectedMatch.type === 'it_courses'
                              ? `${selectedMatch.details?.courseCategory?.name || selectedMatch.details?.course?.name || 'IT Course'}`
                              : ''
                          }
                          message={
                            selectedMatch.type === 'languages' || selectedMatch.type === 'hobbies' || selectedMatch.type === 'it_courses'
                              ? 'Please select your preference:'
                              : 'Please select the subjects you need tutoring for:'
                          }
                        />

                        <div className='space-y-4'>
                          {/* Show loading or no items message */}
                          {selectedMatch && parentId && educationItemOptions.length === 0 && (
                            <div className='p-4 border border-gray-200 rounded-md'>
                              <p className='text-center text-gray-500'>{categoryItemsData ? 'No items found' : 'Loading...'}</p>
                            </div>
                          )}

                          {/* Multi-select for schools, colleges, and exams */}
                          {['schools', 'colleges', 'exams'].includes(selectedMatch.type) && educationItemOptions.length > 0 && (
                            <PrimaryMultiSelectForm
                              form={form}
                              name={
                                selectedMatch.type === 'schools' ? 'subjects' : selectedMatch.type === 'colleges' ? 'collegeSubjects' : 'examSubjects'
                              }
                              label={
                                selectedMatch.type === 'schools'
                                  ? 'Choose Subjects You Need Tutoring For'
                                  : selectedMatch.type === 'colleges'
                                  ? 'Select Subjects'
                                  : 'Select Exam Subjects'
                              }
                              options={educationItemOptions}
                              required
                              placeholder={`Select ${selectedMatch.type === 'exams' ? 'exam ' : ''}subjects`}
                              searchPlaceholder={`Search ${selectedMatch.type === 'exams' ? 'exam ' : ''}subjects...`}
                            />
                          )}

                          {/* Single select for languages, hobbies, and IT courses */}
                          {['languages', 'hobbies', 'it_courses'].includes(selectedMatch.type) && educationItemOptions.length > 0 && (
                            <PrimarySelect
                              form={form}
                              name={selectedMatch.type === 'languages' ? 'language' : selectedMatch.type === 'hobbies' ? 'hobby' : 'course'}
                              label={
                                selectedMatch.type === 'languages'
                                  ? 'Select Language'
                                  : selectedMatch.type === 'hobbies'
                                  ? 'Select Hobby'
                                  : 'Select Course'
                              }
                              options={educationItemOptions}
                              required
                              placeholder={`Select ${
                                selectedMatch.type === 'languages' ? 'language' : selectedMatch.type === 'hobbies' ? 'hobby' : 'course'
                              }`}
                            />
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                <div className='flex justify-between gap-3 border-t border-gray-100 pt-5 mt-6'>
                  <button
                    type='button'
                    onClick={goToPrevTab}
                    className='px-4 py-2 rounded-lg border border-primaryColor-200 text-primaryColor-600 hover:bg-primaryColor-50 hover:text-primaryColor-700 transition-colors flex items-center gap-1'
                  >
                    <ArrowLeft size={16} />
                    <span>Back</span>
                  </button>
                  <button
                    type='button'
                    onClick={goToNextTab}
                    disabled={!isCurrentTabValid()}
                    className={`px-4 py-2 rounded-lg text-white transition-colors flex items-center gap-1 ${
                      isCurrentTabValid()
                        ? 'bg-gradient-to-r from-primaryColor-500 to-primaryColor-600 hover:from-primaryColor-600 hover:to-primaryColor-700'
                        : 'bg-gray-300 cursor-not-allowed'
                    }`}
                  >
                    <span>Next</span>
                    <ArrowLeft size={16} className='rotate-180' />
                  </button>
                </div>
              </div>
            </form>
          </Form>
        </TabsContent>
        {/* Tutor & Timing Tab */}
        <TabsContent value={TABS.TUTOR_TIMING} className='p-0 m-0'>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className='p-6 bg-white rounded-b-xl'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-8'>
                  {/* Left Column */}
                  <div>
                    {/* Timing and Mode */}
                    <div className='mb-6'>
                      <FormHeading icon={Clock} title='Timing and Mode' variant='blue' />
                      <div className='space-y-4'>
                        <PrimarySelect form={form} name='preferences.startTime' label='When to Start' options={startTimeOptions} required />
                        <PrimaryMultiSelectForm
                          form={form}
                          name='preferences.deliveryModes'
                          label='Where do you want the classes?'
                          options={deliveryModeOptions.map((option) => ({
                            ...option,
                            label: option.value === 'online' ? option.label : `At ${option.label}`,
                          }))}
                          required
                          placeholder='Select delivery modes'
                          searchPlaceholder='Search delivery modes...'
                        />
                      </div>
                    </div>
                  </div>
                  {/* Right Column */}
                  <div>
                    {/* Tutor Preferences */}
                    <div className='mb-6'>
                      <FormHeading icon={Users} title='Tutor Preferences' variant='green' />
                      <div className='space-y-4'>
                        <PrimaryInput form={form} name='preferences.classesPerWeek' label='Classes Per Week' type='number' required />
                        <PrimarySelect
                          form={form}
                          name='preferences.tutorGender'
                          label='Preferred Tutor Gender'
                          options={tutorGenderOptions}
                          disabled={
                            form.watch('preferences.deliveryModes')?.length === 1 && form.watch('preferences.deliveryModes')?.includes('institute')
                          }
                          required
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className='flex justify-between gap-3 border-t border-gray-100 pt-5 mt-6'>
                  <button
                    type='button'
                    onClick={goToPrevTab}
                    className='px-4 py-2 rounded-lg border border-primaryColor-200 text-primaryColor-600 hover:bg-primaryColor-50 hover:text-primaryColor-700 transition-colors flex items-center gap-1'
                  >
                    <ArrowLeft size={16} />
                    <span>Back</span>
                  </button>
                  <button
                    type='button'
                    onClick={goToNextTab}
                    disabled={!isCurrentTabValid()}
                    className={`px-4 py-2 rounded-lg text-white transition-colors flex items-center gap-1 ${
                      isCurrentTabValid()
                        ? 'bg-gradient-to-r from-primaryColor-500 to-primaryColor-600 hover:from-primaryColor-600 hover:to-primaryColor-700'
                        : 'bg-gray-300 cursor-not-allowed'
                    }`}
                  >
                    <span>Next</span>
                    <ArrowLeft size={16} className='rotate-180' />
                  </button>
                </div>
              </div>
            </form>
          </Form>
        </TabsContent>
        {/* Message & Student Tab */}
        <TabsContent value={TABS.MESSAGE_STUDENT} className='p-0 m-0'>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className='p-6 bg-white rounded-b-xl'>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-8'>
                  {/* Left Column */}
                  <div>
                    {/* Student Selection */}
                    <div className='mb-6'>
                      <FormHeading icon={Users} title='Student Information' variant='purple' />
                      <div className='space-y-4'>
                        <PrimarySelect form={form} name='childProfileId' label='Student' options={childProfileOptions} required />
                      </div>
                    </div>
                  </div>

                  {/* Right Column */}
                  <div>
                    {/* Special Requirements */}
                    <div className='mb-6'>
                      <FormHeading icon={MessageSquare} title='Special Requirements' variant='secondary' />
                      <div className='space-y-4'>
                        <PrimaryTextarea
                          form={form}
                          name='preferences.specialRequirements'
                          label='Do you have any special requirements, mention here?'
                          placeholder='Enter any special requirements or comments'
                          rows={4}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className='flex justify-between gap-3 border-t border-gray-100 pt-5 mt-6'>
                  <button
                    type='button'
                    onClick={goToPrevTab}
                    className='px-4 py-2 rounded-lg border border-primaryColor-200 text-primaryColor-600 hover:bg-primaryColor-50 hover:text-primaryColor-700 transition-colors flex items-center gap-1'
                  >
                    <ArrowLeft size={16} />
                    <span>Back</span>
                  </button>
                  <SubmitButton
                    isSubmitting={form.formState.isSubmitting || createEnquiry.isPending}
                    label='Create Enquiry'
                    submittingLabel='Creating...'
                    variant='primary'
                  />
                </div>
              </div>
            </form>
          </Form>
        </TabsContent>
      </Tabs>
    </PrimaryModalWithHeader>
  );
};

export default AddEnquiryModal;
