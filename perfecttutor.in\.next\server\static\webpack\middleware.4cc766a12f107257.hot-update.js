"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var _lib_auth_getCurrentUser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth/getCurrentUser */ \"(middleware)/./lib/auth/getCurrentUser.ts\");\n\n\nasync function middleware(request) {\n    const { pathname } = request.nextUrl;\n    if (pathname.startsWith(\"/parent-dash\")) {\n        return userMiddleware(request, \"student\");\n    }\n    // if (pathname.startsWith('/tutor-dash')) {\n    //   return userMiddleware(request, 'tutor');\n    // }\n    if (pathname.startsWith(\"/institute-dash\")) {\n        return userMiddleware(request, \"institute\");\n    }\n    if (pathname.startsWith(\"/school-dash\")) {\n        return userMiddleware(request, \"school\");\n    }\n    if (pathname.startsWith(\"/college-dash\")) {\n        return userMiddleware(request, \"college\");\n    }\n    if (pathname.startsWith(\"/ascrm\")) {\n        return adminMiddleware(request);\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\nasync function adminMiddleware(request) {\n    try {\n        const session = request.cookies.get(\"staff_session\")?.value;\n        if (!session) throw new Error(\"Missing session token\");\n        const staff = await (0,_lib_auth_getCurrentUser__WEBPACK_IMPORTED_MODULE_1__.getCurrentStaff)(session);\n        if (!staff) throw new Error(\"Failed to fetch staff data\");\n        if (staff.role !== \"super_admin\") throw new Error(\"Staff is not an admin\");\n        if (!staff.isActive) throw new Error(\"Staff account is not active\");\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    } catch (error) {\n        console.error(`\\x1b[31mMiddleware:${error.message}\\x1b[0m`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/staff-access\", request.url));\n    }\n}\nasync function userMiddleware(request, userType) {\n    try {\n        const session = request.cookies.get(\"session\")?.value;\n        if (!session) throw new Error(\"Missing session token\");\n        const user = await (0,_lib_auth_getCurrentUser__WEBPACK_IMPORTED_MODULE_1__.getCurrentUser)(session, userType);\n        if (!user) throw new Error(\"Failed to fetch user data\");\n        if (user.userType !== userType) throw new Error(`User is not a ${userType}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    } catch (error) {\n        console.error(`\\x1b[31mMiddleware:${error.message}\\x1b[0m`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/login\", request.url));\n    }\n}\nconst config = {\n    matcher: [\n        \"/parent-dash/:path*\",\n        \"/tutor-dash/:path*\",\n        \"/institute-dash/:path*\",\n        \"/school-dash/:path*\",\n        \"/college-dash/:path*\",\n        \"/ascrm/:path*\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKG1pZGRsZXdhcmUpLy4vbWlkZGxld2FyZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXdEO0FBQ29CO0FBRzdELGVBQWVHLFdBQVdDLE9BQW9CO0lBQzNELE1BQU0sRUFBRUMsUUFBUSxFQUFFLEdBQUdELFFBQVFFLE9BQU87SUFFcEMsSUFBSUQsU0FBU0UsVUFBVSxDQUFDLGlCQUFpQjtRQUN2QyxPQUFPQyxlQUFlSixTQUFTO0lBQ2pDO0lBRUEsNENBQTRDO0lBQzVDLDZDQUE2QztJQUM3QyxJQUFJO0lBRUosSUFBSUMsU0FBU0UsVUFBVSxDQUFDLG9CQUFvQjtRQUMxQyxPQUFPQyxlQUFlSixTQUFTO0lBQ2pDO0lBRUEsSUFBSUMsU0FBU0UsVUFBVSxDQUFDLGlCQUFpQjtRQUN2QyxPQUFPQyxlQUFlSixTQUFTO0lBQ2pDO0lBRUEsSUFBSUMsU0FBU0UsVUFBVSxDQUFDLGtCQUFrQjtRQUN4QyxPQUFPQyxlQUFlSixTQUFTO0lBQ2pDO0lBRUEsSUFBSUMsU0FBU0UsVUFBVSxDQUFDLFdBQVc7UUFDakMsT0FBT0UsZ0JBQWdCTDtJQUN6QjtJQUVBLE9BQU9KLHFEQUFZQSxDQUFDVSxJQUFJO0FBQzFCO0FBRUEsZUFBZUQsZ0JBQWdCTCxPQUFvQjtJQUNqRCxJQUFJO1FBQ0YsTUFBTU8sVUFBVVAsUUFBUVEsT0FBTyxDQUFDQyxHQUFHLENBQUMsa0JBQWtCQztRQUN0RCxJQUFJLENBQUNILFNBQVMsTUFBTSxJQUFJSSxNQUFNO1FBRTlCLE1BQU1DLFFBQVEsTUFBTWYseUVBQWVBLENBQUNVO1FBQ3BDLElBQUksQ0FBQ0ssT0FBTyxNQUFNLElBQUlELE1BQU07UUFFNUIsSUFBSUMsTUFBTUMsSUFBSSxLQUFLLGVBQWUsTUFBTSxJQUFJRixNQUFNO1FBRWxELElBQUksQ0FBQ0MsTUFBTUUsUUFBUSxFQUFFLE1BQU0sSUFBSUgsTUFBTTtRQUVyQyxPQUFPZixxREFBWUEsQ0FBQ1UsSUFBSTtJQUMxQixFQUFFLE9BQU9TLE9BQVk7UUFDbkJDLFFBQVFELEtBQUssQ0FBQyxDQUFDLG1CQUFtQixFQUFFQSxNQUFNRSxPQUFPLENBQUMsT0FBTyxDQUFDO1FBQzFELE9BQU9yQixxREFBWUEsQ0FBQ3NCLFFBQVEsQ0FBQyxJQUFJQyxJQUFJLGlCQUFpQm5CLFFBQVFvQixHQUFHO0lBQ25FO0FBQ0Y7QUFFQSxlQUFlaEIsZUFBZUosT0FBb0IsRUFBRXFCLFFBQXNCO0lBQ3hFLElBQUk7UUFDRixNQUFNZCxVQUFVUCxRQUFRUSxPQUFPLENBQUNDLEdBQUcsQ0FBQyxZQUFZQztRQUNoRCxJQUFJLENBQUNILFNBQVMsTUFBTSxJQUFJSSxNQUFNO1FBRTlCLE1BQU1XLE9BQU8sTUFBTXhCLHdFQUFjQSxDQUFDUyxTQUFTYztRQUMzQyxJQUFJLENBQUNDLE1BQU0sTUFBTSxJQUFJWCxNQUFNO1FBRTNCLElBQUlXLEtBQUtELFFBQVEsS0FBS0EsVUFBVSxNQUFNLElBQUlWLE1BQU0sQ0FBQyxjQUFjLEVBQUVVLFNBQVMsQ0FBQztRQUUzRSxPQUFPekIscURBQVlBLENBQUNVLElBQUk7SUFDMUIsRUFBRSxPQUFPUyxPQUFZO1FBQ25CQyxRQUFRRCxLQUFLLENBQUMsQ0FBQyxtQkFBbUIsRUFBRUEsTUFBTUUsT0FBTyxDQUFDLE9BQU8sQ0FBQztRQUMxRCxPQUFPckIscURBQVlBLENBQUNzQixRQUFRLENBQUMsSUFBSUMsSUFBSSxVQUFVbkIsUUFBUW9CLEdBQUc7SUFDNUQ7QUFDRjtBQUVPLE1BQU1HLFNBQVM7SUFDcEJDLFNBQVM7UUFBQztRQUF1QjtRQUFzQjtRQUEwQjtRQUF1QjtRQUF3QjtLQUFnQjtBQUNsSixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL21pZGRsZXdhcmUudHM/NDIyZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVzcG9uc2UsIE5leHRSZXF1ZXN0IH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xyXG5pbXBvcnQgeyBnZXRDdXJyZW50U3RhZmYsIGdldEN1cnJlbnRVc2VyIH0gZnJvbSAnQC9saWIvYXV0aC9nZXRDdXJyZW50VXNlcic7XHJcbmltcG9ydCB7IElVc2VyVHlwZU1hcCB9IGZyb20gJy4vdmFsaWRhdGlvbi9zY2hlbWFzL21hcHMnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24gbWlkZGxld2FyZShyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xyXG4gIGNvbnN0IHsgcGF0aG5hbWUgfSA9IHJlcXVlc3QubmV4dFVybDtcclxuXHJcbiAgaWYgKHBhdGhuYW1lLnN0YXJ0c1dpdGgoJy9wYXJlbnQtZGFzaCcpKSB7XHJcbiAgICByZXR1cm4gdXNlck1pZGRsZXdhcmUocmVxdWVzdCwgJ3N0dWRlbnQnKTtcclxuICB9XHJcblxyXG4gIC8vIGlmIChwYXRobmFtZS5zdGFydHNXaXRoKCcvdHV0b3ItZGFzaCcpKSB7XHJcbiAgLy8gICByZXR1cm4gdXNlck1pZGRsZXdhcmUocmVxdWVzdCwgJ3R1dG9yJyk7XHJcbiAgLy8gfVxyXG5cclxuICBpZiAocGF0aG5hbWUuc3RhcnRzV2l0aCgnL2luc3RpdHV0ZS1kYXNoJykpIHtcclxuICAgIHJldHVybiB1c2VyTWlkZGxld2FyZShyZXF1ZXN0LCAnaW5zdGl0dXRlJyk7XHJcbiAgfVxyXG5cclxuICBpZiAocGF0aG5hbWUuc3RhcnRzV2l0aCgnL3NjaG9vbC1kYXNoJykpIHtcclxuICAgIHJldHVybiB1c2VyTWlkZGxld2FyZShyZXF1ZXN0LCAnc2Nob29sJyk7XHJcbiAgfVxyXG5cclxuICBpZiAocGF0aG5hbWUuc3RhcnRzV2l0aCgnL2NvbGxlZ2UtZGFzaCcpKSB7XHJcbiAgICByZXR1cm4gdXNlck1pZGRsZXdhcmUocmVxdWVzdCwgJ2NvbGxlZ2UnKTtcclxuICB9XHJcblxyXG4gIGlmIChwYXRobmFtZS5zdGFydHNXaXRoKCcvYXNjcm0nKSkge1xyXG4gICAgcmV0dXJuIGFkbWluTWlkZGxld2FyZShyZXF1ZXN0KTtcclxuICB9XHJcblxyXG4gIHJldHVybiBOZXh0UmVzcG9uc2UubmV4dCgpO1xyXG59XHJcblxyXG5hc3luYyBmdW5jdGlvbiBhZG1pbk1pZGRsZXdhcmUocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcclxuICB0cnkge1xyXG4gICAgY29uc3Qgc2Vzc2lvbiA9IHJlcXVlc3QuY29va2llcy5nZXQoJ3N0YWZmX3Nlc3Npb24nKT8udmFsdWU7XHJcbiAgICBpZiAoIXNlc3Npb24pIHRocm93IG5ldyBFcnJvcignTWlzc2luZyBzZXNzaW9uIHRva2VuJyk7XHJcblxyXG4gICAgY29uc3Qgc3RhZmYgPSBhd2FpdCBnZXRDdXJyZW50U3RhZmYoc2Vzc2lvbik7XHJcbiAgICBpZiAoIXN0YWZmKSB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBmZXRjaCBzdGFmZiBkYXRhJyk7XHJcblxyXG4gICAgaWYgKHN0YWZmLnJvbGUgIT09ICdzdXBlcl9hZG1pbicpIHRocm93IG5ldyBFcnJvcignU3RhZmYgaXMgbm90IGFuIGFkbWluJyk7XHJcblxyXG4gICAgaWYgKCFzdGFmZi5pc0FjdGl2ZSkgdGhyb3cgbmV3IEVycm9yKCdTdGFmZiBhY2NvdW50IGlzIG5vdCBhY3RpdmUnKTtcclxuXHJcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLm5leHQoKTtcclxuICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICBjb25zb2xlLmVycm9yKGBcXHgxYlszMW1NaWRkbGV3YXJlOiR7ZXJyb3IubWVzc2FnZX1cXHgxYlswbWApO1xyXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5yZWRpcmVjdChuZXcgVVJMKCcvc3RhZmYtYWNjZXNzJywgcmVxdWVzdC51cmwpKTtcclxuICB9XHJcbn1cclxuXHJcbmFzeW5jIGZ1bmN0aW9uIHVzZXJNaWRkbGV3YXJlKHJlcXVlc3Q6IE5leHRSZXF1ZXN0LCB1c2VyVHlwZTogSVVzZXJUeXBlTWFwKSB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IHNlc3Npb24gPSByZXF1ZXN0LmNvb2tpZXMuZ2V0KCdzZXNzaW9uJyk/LnZhbHVlO1xyXG4gICAgaWYgKCFzZXNzaW9uKSB0aHJvdyBuZXcgRXJyb3IoJ01pc3Npbmcgc2Vzc2lvbiB0b2tlbicpO1xyXG5cclxuICAgIGNvbnN0IHVzZXIgPSBhd2FpdCBnZXRDdXJyZW50VXNlcihzZXNzaW9uLCB1c2VyVHlwZSk7XHJcbiAgICBpZiAoIXVzZXIpIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGZldGNoIHVzZXIgZGF0YScpO1xyXG5cclxuICAgIGlmICh1c2VyLnVzZXJUeXBlICE9PSB1c2VyVHlwZSkgdGhyb3cgbmV3IEVycm9yKGBVc2VyIGlzIG5vdCBhICR7dXNlclR5cGV9YCk7XHJcblxyXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5uZXh0KCk7XHJcbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xyXG4gICAgY29uc29sZS5lcnJvcihgXFx4MWJbMzFtTWlkZGxld2FyZToke2Vycm9yLm1lc3NhZ2V9XFx4MWJbMG1gKTtcclxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UucmVkaXJlY3QobmV3IFVSTCgnL2xvZ2luJywgcmVxdWVzdC51cmwpKTtcclxuICB9XHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCBjb25maWcgPSB7XHJcbiAgbWF0Y2hlcjogWycvcGFyZW50LWRhc2gvOnBhdGgqJywgJy90dXRvci1kYXNoLzpwYXRoKicsICcvaW5zdGl0dXRlLWRhc2gvOnBhdGgqJywgJy9zY2hvb2wtZGFzaC86cGF0aConLCAnL2NvbGxlZ2UtZGFzaC86cGF0aConLCAnL2FzY3JtLzpwYXRoKiddLFxyXG59O1xyXG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiZ2V0Q3VycmVudFN0YWZmIiwiZ2V0Q3VycmVudFVzZXIiLCJtaWRkbGV3YXJlIiwicmVxdWVzdCIsInBhdGhuYW1lIiwibmV4dFVybCIsInN0YXJ0c1dpdGgiLCJ1c2VyTWlkZGxld2FyZSIsImFkbWluTWlkZGxld2FyZSIsIm5leHQiLCJzZXNzaW9uIiwiY29va2llcyIsImdldCIsInZhbHVlIiwiRXJyb3IiLCJzdGFmZiIsInJvbGUiLCJpc0FjdGl2ZSIsImVycm9yIiwiY29uc29sZSIsIm1lc3NhZ2UiLCJyZWRpcmVjdCIsIlVSTCIsInVybCIsInVzZXJUeXBlIiwidXNlciIsImNvbmZpZyIsIm1hdGNoZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});