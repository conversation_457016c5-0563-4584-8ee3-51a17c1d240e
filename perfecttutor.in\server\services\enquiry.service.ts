import apiClient from '@/server/apiClient';
import { IServiceCategoryMap, serviceCategoryMap } from '@/validation/schemas/education/index.maps';
import { ITutorGenderMap, IStartTimeMap, IDeliveryModeMap, IEnquiryStatusMap } from '@/validation/schemas/enquiry.maps';
import { CreateEnquiryInput } from '@/validation/schemas/enquiry.schema';
import { IChildProfileDocument } from './profile.service';
import { IBoardDocument, IClassDocument, ISubjectDocument } from './education/school.service';
import { IStreamDocument, IDegreeLevelDocument, IDegreeDocument, IBranchDocument, ICollegeSubjectDocument } from './education/college.service';
import { IHobbyTypeDocument, IHobbyDocument } from './education/hobby.service';
import { ILanguageTypeDocument, ILanguageDocument } from './education/language.service';
import { ICourseTypeDocument, ICourseDocument } from './education/course.service';
import { IExamCategoryDocument, IExamDocument, IExamSubjectDocument } from './education/exam.service';
import { IPagination } from '@/types/api';

interface ISearchResponseItem {
  id: string;
  name: string;
}

export interface ISearchResponse {
  matches: [
    {
      type: IServiceCategoryMap;
      details: {
        // School fields
        subject?: ISearchResponseItem;
        class?: ISearchResponseItem;
        board?: ISearchResponseItem;
        subjects?: [ISearchResponseItem];

        // College fields
        branch?: ISearchResponseItem;
        degree?: ISearchResponseItem;
        stream?: ISearchResponseItem;

        // Hobby fields
        hobby?: ISearchResponseItem;
        hobbyType?: ISearchResponseItem;

        // Language fields
        language?: ISearchResponseItem;
        languageType?: ISearchResponseItem;

        // Course fields
        course?: ISearchResponseItem;
        courseCategory?: ISearchResponseItem;

        // Exam fields
        exam?: ISearchResponseItem;
        examCategory?: ISearchResponseItem;
        examSubjects?: [ISearchResponseItem];
      };
      displayText: string;
    }
  ];
}

// EDUCATION ITEMS
export interface IEducationItem {
  _id: string;
  name: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ICategoryItemsResponse {
  subjects?: IEducationItem[];
  languages?: IEducationItem[];
  hobbies?: IEducationItem[];
  courses?: IEducationItem[];
  examSubjects?: IEducationItem[];
  metadata: {
    // 1. School
    class?: IClassDocument;
    board?: IBoardDocument;
    // 2. College
    branch?: IBranchDocument;
    degree?: IDegreeDocument;
    degreeLevel?: IDegreeLevelDocument;
    stream?: IStreamDocument;
    // 3. Language
    languageType?: ILanguageTypeDocument;
    // 4. Hobby
    hobbyType?: IHobbyTypeDocument;
    // 5. Course
    courseType?: ICourseTypeDocument;
    // 6. Exam
    exam?: IExamDocument;
    examCategory?: IExamCategoryDocument;
  };
}

interface IEducationItemOption {
  id: string;
  value: string;
  label: string;
}

export function createEducationItemOptions(items: IEducationItem[]): IEducationItemOption[] {
  return items.map((item) => ({ id: item._id, value: item._id, label: item.name }));
}

// ENQUIRY
export interface IEnquiryDocument {
  user: string;
  childProfile: string;
  category: keyof typeof serviceCategoryMap;

  // School specific fields
  board?: string;
  class?: string;
  subjects?: string[];
  allSubjects?: boolean;

  // Populated fields
  boardDetails?: IBoardDocument;
  classDetails?: IClassDocument;
  subjectDetails?: ISubjectDocument[];

  // College specific fields
  stream?: string;
  degreeLevel?: string;
  degree?: string;
  branch?: string;
  collegeSubjects?: string[];

  // Populated fields
  streamDetails?: IStreamDocument;
  degreeLevelDetails?: IDegreeLevelDocument;
  degreeDetails?: IDegreeDocument;
  branchDetails?: IBranchDocument;
  collegeSubjectDetails?: ICollegeSubjectDocument[];

  // Hobby specific fields
  hobbyType?: string;
  hobby?: string;

  // Populated fields
  hobbyTypeDetails?: IHobbyTypeDocument;
  hobbyDetails?: IHobbyDocument;

  // Language specific fields
  languageType?: string;
  language?: string;

  // Populated fields
  languageTypeDetails?: ILanguageTypeDocument;
  languageDetails?: ILanguageDocument;

  // Course specific fields
  courseType?: string;
  course?: string;

  // Populated fields
  courseTypeDetails?: ICourseTypeDocument;
  courseDetails?: ICourseDocument;

  // Exam specific fields
  examCategory?: string;
  exam?: string;
  examSubjects?: string[];

  // Populated fields
  examCategoryDetails?: IExamCategoryDocument;
  examDetails?: IExamDocument;
  examSubjectDetails?: IExamSubjectDocument[];

  // Preferences
  location: {
    address: string;
    landmark?: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  tutorGender: ITutorGenderMap;
  classesPerWeek: number;
  startTime: IStartTimeMap;
  deliveryModes: IDeliveryModeMap[];
  specialRequirements?: string;

  // Status
  status: IEnquiryStatusMap;
  isActive: boolean;

  // Populated fields
  _id: string;
  createdAt: string;
  updatedAt: string;
  childProfileDetails?: IChildProfileDocument;
}

export interface IEnquiryResponse {
  enquiry: IEnquiryDocument;
}

export interface IEnquiriesResponse {
  enquiries: IEnquiryDocument[];
  pagination: IPagination;
}

const enquiryService = {
  search: async (query: string) => await apiClient.post<ISearchResponse>(`/enquiries/search`, { query }),
  getCategoryItems: async (type: IServiceCategoryMap, id: string) =>
    await apiClient.get<ICategoryItemsResponse>(`/enquiries/category/${type}?id=${id}`),
  createEnquiry: async (data: CreateEnquiryInput) => await apiClient.post<IEnquiryResponse>('/enquiries/create', data),
  getParentEnquiries: async (params?: Record<string, any>) => {
    const queryParams = new URLSearchParams(params);
    return await apiClient.get<IEnquiriesResponse>(`/enquiries/parent?${queryParams.toString()}`);
  },
  getParentEnquiryById: async (id: string) => await apiClient.get<IEnquiryResponse>(`/enquiries/parent/${id}`),
  updateEnquiryStatus: async (id: string, isActive: boolean) =>
    await apiClient.patch<IEnquiriesResponse>(`/enquiries/parent/${id}/status`, { isActive }),
};

export { enquiryService };
