import { useQuery, useMutation } from '@tanstack/react-query';
import { enquiryService } from '@/server/services/enquiry.service';
import { QUERY_KEYS } from '@/lib/react-query/queryKeys';
import { queryClient } from '@/lib/react-query/queryClient';
import { CreateEnquiryInput } from '@/validation/schemas/enquiry.schema';
import { IServiceCategoryMap } from '@/validation/schemas/education/index.maps';

export function useSearchEnquiries(query: string) {
  return useQuery({
    queryKey: [QUERY_KEYS.ENQUIRY.SEARCH, query],
    queryFn: () => enquiryService.search(query),
    enabled: !!query && query.length > 1,
  });
}

export function useGetCategoryItems(type: IServiceCategoryMap, id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.ENQUIRY.CATEGORY_ITEMS, type, id],
    queryFn: () => enquiryService.getCategoryItems(type, id),
    enabled: !!type && !!id,
    ...options,
  });
}

export function useCreateEnquiry() {
  return useMutation({
    mutationFn: (data: CreateEnquiryInput) => enquiryService.createEnquiry(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ENQUIRY.PARENT_ENQUIRIES] });
    },
  });
}

export function useGetParentEnquiries(params?: Record<string, any>, options = {}) {
  return useQuery({ queryKey: [QUERY_KEYS.ENQUIRY.PARENT_ENQUIRIES, params], queryFn: () => enquiryService.getParentEnquiries(params), ...options });
}

export function useGetParentEnquiryById(id: string, options = {}) {
  return useQuery({
    queryKey: [QUERY_KEYS.ENQUIRY.PARENT_ENQUIRY, id],
    queryFn: () => enquiryService.getParentEnquiryById(id),
    enabled: !!id,
    ...options,
  });
}

export function useUpdateEnquiryStatus() {
  return useMutation({
    mutationFn: ({ id, isActive }: { id: string; isActive: boolean }) => enquiryService.updateEnquiryStatus(id, isActive),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ENQUIRY.PARENT_ENQUIRIES] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ENQUIRY.PARENT_ENQUIRY, variables.id] });
    },
  });
}
